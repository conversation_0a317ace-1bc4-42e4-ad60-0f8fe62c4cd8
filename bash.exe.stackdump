Stack trace:
Frame         Function      Args
0007FFFFA4D0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFA4D0, 0007FFFF93D0) msys-2.0.dll+0x2118E
0007FFFFA4D0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA7A8) msys-2.0.dll+0x69BA
0007FFFFA4D0  0002100469F2 (00021028DF99, 0007FFFFA388, 0007FFFFA4D0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA4D0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA4D0  00021006A545 (0007FFFFA4E0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA7B0  00021006B9A5 (0007FFFFA4E0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA3FB20000 ntdll.dll
7FFA3EB80000 KERNEL32.DLL
7FFA3D4D0000 KERNELBASE.dll
7FFA3E300000 USER32.dll
7FFA3D2A0000 win32u.dll
7FFA3F740000 GDI32.dll
7FFA3CF40000 gdi32full.dll
7FFA3D2D0000 msvcp_win.dll
7FFA3CC70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA3E200000 advapi32.dll
7FFA3E840000 msvcrt.dll
7FFA3EE00000 sechost.dll
7FFA3E8F0000 RPCRT4.dll
7FFA3C220000 CRYPTBASE.DLL
7FFA3D200000 bcryptPrimitives.dll
7FFA3E2C0000 IMM32.DLL
